<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sprite Sheet Animation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .canvas-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 10px;
        }
        
        #characterCanvas {
            border: 1px solid #ddd;
            background: #f9f9f9;
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Sprite Sheet Animation Test</h1>
        
        <div class="info">
            <h3>How it works:</h3>
            <p>This test loads a single sprite sheet image and displays different animations by extracting frames from specific grid positions. This eliminates the need for hundreds of individual image API calls.</p>
            <ul>
                <li><strong>Sprite Sheet:</strong> A single image containing all animation frames in a grid</li>
                <li><strong>Canvas Rendering:</strong> Extract and display specific frames using canvas drawImage</li>
                <li><strong>Performance:</strong> 1 HTTP request instead of 800+ individual image requests</li>
            </ul>
        </div>
        
        <div class="canvas-container">
            <h3>Character Animation</h3>
            <canvas id="characterCanvas" width="100" height="100"></canvas>
            <div id="status" class="status">Loading sprite sheet...</div>
        </div>
        
        <div class="controls">
            <button onclick="playAnimation('idle')" id="idleBtn">Idle</button>
            <button onclick="playAnimation('thinking')" id="thinkingBtn">Thinking</button>
            <button onclick="playAnimation('writing')" id="writingBtn">Writing</button>
            <button onclick="playAnimation('searching')" id="searchingBtn">Searching</button>
            <button onclick="playAnimation('getAttention')" id="getAttentionBtn">Get Attention</button>
            <button onclick="playAnimation('congratulations')" id="congratulationsBtn">Congratulations</button>
            <button onclick="playAnimation('reading')" id="readingBtn">Reading</button>
            <button onclick="playAnimation('look')" id="lookBtn">Look</button>
            <button onclick="stopAnimation()" id="stopBtn">Stop</button>
        </div>
        
        <div class="info">
            <h3>Animation Definitions:</h3>
            <p>Each animation is defined by its position in the sprite sheet grid:</p>
            <ul>
                <li><strong>Row:</strong> Which row in the sprite sheet (0-based)</li>
                <li><strong>Start Column:</strong> Starting frame position in that row</li>
                <li><strong>Frame Count:</strong> Number of frames in the animation</li>
                <li><strong>Duration:</strong> Total time for one complete animation cycle</li>
            </ul>
        </div>
    </div>

    <script src="/js/sprite-sheet-helper.js"></script>
    <script>
        let animator = null;
        const statusDiv = document.getElementById('status');
        
        // Animation definitions matching your Vue component
        const animations = {
            idle: { row: 0, startCol: 0, frameCount: 20, duration: 3000 },
            thinking: { row: 0, startCol: 20, frameCount: 22, duration: 2500 },
            getAttention: { row: 1, startCol: 0, frameCount: 29, duration: 2000 },
            writing: { row: 3, startCol: 0, frameCount: 21, duration: 2000 },
            searching: { row: 4, startCol: 0, frameCount: 54, duration: 3000 },
            congratulations: { row: 5, startCol: 0, frameCount: 34, duration: 3000 },
            reading: { row: 6, startCol: 0, frameCount: 85, duration: 4000 },
            look: { row: 7, startCol: 0, frameCount: 56, duration: 5000 }
        };
        
        async function initializeAnimator() {
            try {
                animator = new SpriteSheetAnimator('characterCanvas', '/images/kairu/sprite_sheet.png', 10, 10);
                await animator.loadSpriteSheet('/images/kairu/sprite_sheet.png');
                
                statusDiv.textContent = '✅ Sprite sheet loaded successfully! Click any animation button.';
                statusDiv.className = 'status';
                
                // Enable all buttons
                document.querySelectorAll('button').forEach(btn => btn.disabled = false);
                
                // Start with idle animation
                playAnimation('idle');
                
            } catch (error) {
                console.error('Failed to load sprite sheet:', error);
                statusDiv.textContent = '❌ Failed to load sprite sheet. Check console for details.';
                statusDiv.className = 'status error';
            }
        }
        
        function playAnimation(animationName) {
            if (!animator) return;
            
            const anim = animations[animationName];
            if (!anim) {
                console.warn('Unknown animation:', animationName);
                return;
            }
            
            animator.playAnimation(anim.row, anim.startCol, anim.frameCount, anim.duration, true);
            statusDiv.textContent = `🎬 Playing: ${animationName} (Row: ${anim.row}, Frames: ${anim.frameCount})`;
            statusDiv.className = 'status';
        }
        
        function stopAnimation() {
            if (animator) {
                animator.stopAnimation();
                statusDiv.textContent = '⏹️ Animation stopped';
                statusDiv.className = 'status';
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', () => {
            // Disable all buttons initially
            document.querySelectorAll('button').forEach(btn => btn.disabled = true);
            initializeAnimator();
        });
    </script>
</body>
</html>
