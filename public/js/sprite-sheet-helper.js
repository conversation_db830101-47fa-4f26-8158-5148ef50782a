/**
 * Sprite Sheet Animation Helper
 * 
 * This helper demonstrates how to use the sprite sheet system for character animations.
 * The sprite sheet should be organized in a grid format where each row represents
 * a different animation and each column represents a frame in that animation.
 * 
 * Example sprite sheet layout (10x10 grid):
 * Row 0: idle animation (frames 0-19)
 * Row 1: thinking animation (frames 0-21) 
 * Row 2: get_attention animation (frames 0-28)
 * Row 3: writing animation (frames 0-20)
 * Row 4: searching animation (frames 0-53)
 * Row 5: congratulations animation (frames 0-33)
 * Row 6: reading animation (frames 0-84)
 * Row 7: look animation (frames 0-55)
 * Row 8: idle02 animation (frames 0-38)
 * Row 9: idle03 animation (frames 0-27)
 */

class SpriteSheetAnimator {
    constructor(canvasId, spriteSheetPath, cols = 10, rows = 10) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.spriteSheet = null;
        this.cols = cols;
        this.rows = rows;
        this.frameWidth = 0;
        this.frameHeight = 0;
        this.currentFrame = 0;
        this.animationTimer = null;
        
        this.loadSpriteSheet(spriteSheetPath);
    }
    
    async loadSpriteSheet(path) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.spriteSheet = img;
                this.frameWidth = img.width / this.cols;
                this.frameHeight = img.height / this.rows;
                
                // Set canvas size to match frame size
                this.canvas.width = this.frameWidth;
                this.canvas.height = this.frameHeight;
                
                console.log(`Sprite sheet loaded: ${img.width}x${img.height}`);
                console.log(`Frame size: ${this.frameWidth}x${this.frameHeight}`);
                resolve();
            };
            img.onerror = reject;
            img.src = path;
        });
    }
    
    renderFrame(row, col) {
        if (!this.spriteSheet) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Calculate source coordinates
        const sourceX = col * this.frameWidth;
        const sourceY = row * this.frameHeight;
        
        // Draw frame
        this.ctx.drawImage(
            this.spriteSheet,
            sourceX, sourceY, this.frameWidth, this.frameHeight,
            0, 0, this.canvas.width, this.canvas.height
        );
    }
    
    playAnimation(row, startCol, frameCount, duration, loop = true) {
        if (this.animationTimer) {
            clearInterval(this.animationTimer);
        }
        
        let currentCol = startCol;
        const frameDuration = duration / frameCount;
        
        this.animationTimer = setInterval(() => {
            this.renderFrame(row, currentCol);
            currentCol++;
            
            if (currentCol >= startCol + frameCount) {
                if (loop) {
                    currentCol = startCol;
                } else {
                    clearInterval(this.animationTimer);
                }
            }
        }, frameDuration);
        
        // Render first frame immediately
        this.renderFrame(row, startCol);
    }
    
    stopAnimation() {
        if (this.animationTimer) {
            clearInterval(this.animationTimer);
            this.animationTimer = null;
        }
    }
}

// Example usage:
// const animator = new SpriteSheetAnimator('characterCanvas', '/images/kairu/sprite_sheet.png');
// animator.playAnimation(0, 0, 20, 3000, true); // Play idle animation
