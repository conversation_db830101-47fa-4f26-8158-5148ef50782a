<template>
  <div>
    <TableFilter :filter.sync="filter" :drawer.sync="filterDrawer" class="table-filter">
      <v-row>
        <v-col class="pa-2">
          <span class="filter-span">
            申請履歴検索
          </span>
        </v-col>
      </v-row>

      <v-row class="mb-6">
        <v-col class="pa-2">
          <TodayDeadlineAtFilter
            :filter="filter"
            target="internal_deadline_at"
          />
        </v-col>
        <!-- 翌営業日締め切り -->
        <v-col class="pa-2">
          <NextBusinessDayDeadline
            :filter="filter"
            :holidays="holidays"
            target="internal_deadline_at"
          />
        </v-col>
      </v-row>


      <!-- No -->
      <v-row>
        <v-col cols="3" class="pa-2">
          <v-select
            v-model="filter.company_code"
            :items="companyCode"
            label="会社記号"
            item-value="company_code"
            item-text="company_code"
            outlined
            dense
            density="comfortable"
          />
        </v-col>
        <v-col cols="1" class="pt-4">
          ー
        </v-col>
        <v-col class="pa-2">
          <v-text-field
            v-model="filter.proof_number"
            label="No."
            name="no"
            class="pb-1"
            clearable
            outlined
            dense
          />
        </v-col>
      </v-row>


      <!-- 倫理確認申請名(title) -->
      <v-row>
        <v-col class="pa-2">
          <v-text-field
            v-model="filter.title"
            label="件名"
            name="title"
            class="pb-1"
            clearable
            outlined
            dense
          />
        </v-col>
      </v-row>

      <!-- ステータス(status) -->
      <v-row>
        <v-col class="pa-2">
          <v-select
            v-model="filter.proof_status_id"
            :items="$page.props.proofStatuses"
            label="ステータス"
            item-value="id"
            item-text="text"
            multiple
            outlined
            dense
            density="comfortable"
          />
        </v-col>
      </v-row>

      <!-- 種別 -->
      <v-row>
        <v-col class="pa-2">
          <v-select
            v-model="filter.categories"
            :items="$page.props.categories"
            label="種別"
            item-value="id"
            item-text="name"
            multiple
            outlined
            dense
          />
        </v-col>
      </v-row>

      <!-- IP -->
      <v-row>
        <v-col class="pa-2">
          <v-select
            v-model="filter.ips"
            :items="$page.props.ips"
            label="IP"
            item-value="id"
            item-text="name"
            multiple
            outlined
            dense
          />
        </v-col>
      </v-row>

      <!-- 外部締切日(internal_deadline_at) -->
      <v-row>
        <v-col class="pa-2">
          <DateTimePicker
            label="締切日時開始"
            v-model="filter.internal_deadline_at.from"
            :textFieldProps="{
              dense: true,
              outlined: true,
            }"
          />
        </v-col>
        <span class="d-flex align-center pb-7">~</span>
        <v-col class="pa-2">
          <DateTimePicker
            label="締切日時終了"
            v-model="filter.internal_deadline_at.to"
            :textFieldProps="{
              dense: true,
              outlined: true,
            }"
          />
        </v-col>
      </v-row>
    </TableFilter>

    <h1 class="ml-3" style="font-weight: bold; font-size: 32px; height: 100px; color: rgba(64, 64, 64, 1);">申請履歴</h1>

    <div class="d-flex mr-4">
      <BackButton />
    </div>

    <v-data-table
      :headers="headers"
      :items="filteredProofs"
      :items-per-page="paginator.per_page"
      class="row-pointer mb-4 mt-4"
      elevation="0"
      no-data-text="倫理確認申請データが存在しません"
      hide-default-footer
      dense
      disable-sort
    >
      <template v-slot:top>
        <v-toolbar flat class="checklist-title">
          <v-toolbar-title>
          </v-toolbar-title>
          <v-spacer></v-spacer>
          <AicSortButton :filterDrawer.sync="filterDrawer" />
        </v-toolbar>
      </template>

      <!-- 申請ID -->
      <template v-slot:[`item.id`]="{ item }">
        <span class="table-text" >{{ item.proof_number }}</span>
      </template>

      <template v-slot:[`item.internal_deadline_at`]="{ item }">
        <span class="table-text" style="margin: 0%;">{{ formatDateTime(item.internal_deadline_at) }}</span>
      </template>

      <!-- 申請名 -->
      <template v-slot:[`item.title`]="{ item }">
        <span style="margin: 0%;" class="table-text-title">{{ item.title }}</span>
        <div class="chip-container">
          <v-chip class="custom-chip" color="primary mr-1" v-if="hasNotification(item)" rounded small>
            <span class="table-text">{{ getNotificationType(item) }}</span>
          </v-chip>
          <!-- proof.category.name -->
          <v-chip color="success" class="custom-chip mr-1" rounded small v-for="category in item.categories" :key="category.id">
            <span class="table-text">{{ category.short_name }}</span>
          </v-chip>
          <!-- proof.ips[].name -->
          <v-chip v-for="ip in item.ips" color="success" class="custom-chip mr-1" rounded small :key="ip.id">
            <span class="table-text"> {{ ip.name }}</span>
          </v-chip>
        </div>
      </template>

      <!-- ステータス -->
      <template v-slot:[`item.proof_status_id`]="{ item }">
        <ProofStatus :proof="item" has-batch dense />
      </template>

      <!-- PDFダウンロード -->
      <template v-slot:[`item.convertFile`]="{ item }">
        <div>
          <v-btn
          v-if="!isDownloading[getThumbnailId(item.convertFile)]"
          color="primary"
          :disabled="item.convertFile.length == 0"
          icon
          class="white--text"
          @click="closedOriginFileZipDownload(item)"
          >
            <v-icon center dark size="45">
              mdi-file-download
            </v-icon>
          </v-btn>
            <v-progress-circular
              v-if="isDownloading[getThumbnailId(item.convertFile)]"
              indeterminate
              color="primary"
              class="progress-icon mx-3"
              size="15"
              width="2"
              style="height: 25px; width: 25px; z-index: 100; position: sticky;"
            />
        </div>
      </template>

      <template v-slot:[`item.closedFile`]="{ item }">
        <div>
          <v-btn
          v-if="!isDownloading[getThumbnailId(item.closedFile)]"
          :disabled="item.closedFile.length == 0"
          color="primary"
          icon
          class="white--text"
          @click="closePdfDownload(item.closedFile, item)"
          >
            <v-icon center dark size="45">
              mdi-file-download
            </v-icon>
          </v-btn>
            <v-progress-circular
              v-if="isDownloading[getThumbnailId(item.closedFile)]"
              indeterminate
              color="primary"
              class="progress-icon mx-3"
              size="15"
              width="2"
              style="height: 25px; width: 25px; z-index: 100; position: sticky;"
            />
        </div>
      </template>

      <!-- FOOTER -->
      <template v-slot:footer="{}">
        <Pagination :paginator="paginator" />
      </template>
    </v-data-table>

    <!-- AIC Assistant for Requester -->
    <template v-if="isRequester()">
      <AICAssistant />
    </template>
  </div>
</template>

<script>
import NewLayout from '@/Layouts/NewLayout';
import Pagination from '@/Components/AicPagination';
import TableFilter from '@/Components/NewTableFilter';
import TodayDeadlineAtFilter from '@/Components/AicTodayDeadlineAtFilter';
import NextBusinessDayDeadline from '@/Components/NextBusinessDayDeadline.vue';
import DateTimePicker from '@/Components/NewDateTimePicker';
import ProofStatus from '@/Components/NewProofStatus';
import AvatarGroup from '@/Components/User/AvatarGroup.vue';
import MyCompletionProof from '@/Components/Proof/MyCompletionProof.vue';
import NotMyCompletionProof from '@/Components/Proof/NotMyCompletionProof.vue';
import MySpecialSupporterProof from '@/Components/Proof/MySpecialSupporterProof.vue';
import { previousMonday } from 'date-fns';
import AicSortButton from '@/Components/AicSortButton.vue';
import BackButton from '@/Components/AicBackButton';
import S3 from '@/lib/AWS/S3';
import AICAssistant from '@/Pages/Assistant/AICAssistant.vue';

export default {
  name: 'RequesterProofHistory',
  layout: NewLayout,
  props: {
    paginator: {
      type: Object,
      required: true,
    },
    user: {
      type: Object,
      required: true,
    },
    holidays: {
      type: Array,
      required: true,
    },
    companyCode: {
      type: Array,
      required: true,
    },
  },

  components: {
    Pagination,
    TableFilter,
    NextBusinessDayDeadline,
    TodayDeadlineAtFilter,
    DateTimePicker,
    ProofStatus,
    AvatarGroup,
    MyCompletionProof,
    NotMyCompletionProof,
    MySpecialSupporterProof,
    AicSortButton,
    BackButton,
    AICAssistant,
  },

  computed: {
    filterIconColor() {
      return location.search ? 'success' : 'secondary';
    },
    filteredProofs() {
      if (this.filter.my_completion_only) {
        return this.proofs.filter(proof => proof.completion_marks.some(mark => mark.user_id === this.user.id));
      }

      if (this.filter.idt_my_completion_only) {
        return this.proofs.filter(proof => !proof.completion_marks.some(mark => mark.user_id === this.user.id));
      }

      if(this.filter.my_special_sup_only){
        return this.proofs.filter(proof => proof.members.some(member => member.user_id === this.user.id));
      }
      return this.proofs;
    },
  },

  data() {
    return {
      filterDrawer: false,
      filter: {
        user_id: [],
        user_name: null,
        title: null,
        proof_status_id: [],
        categories: [],
        ips: [],
        external_deadline_at: {
          from: null,
          to: null,
        },
        internal_deadline_at: {
          from: null,
          to: null,
        },
        created_at: {
          from: null,
          to: null,
        },
        id: [],
        my_special_sup_only: false,
        proof_number: '',
        // id_title: "XP-",
        checkedOnly: false,
        unCheckedOnly: false,
        company_code: this.companyCode[0],
      },
      headers: [
        { text: '申請No.', value: 'id', width: '5%' },
        { text: '締め切り日時', value: 'internal_deadline_at', width: '5%' },
        { text: '件名', value: 'title', width: '30%' },
        { text: '申請原本', value: 'convertFile', width: '1%' },
        { text: '注釈済み', value: 'closedFile', width: '1%' },
        { text: 'ステータス', value: 'proof_status_id', width: '1%' },
      ],
      proofs: [],
      isDownloading: {},
      avatarGroup: {
        avatarSize: 42,
        maxDisplay: 0,
        margin: -16,
      },
    };
  },
  methods: {
    hasNotification(proof) {
      return this.getNotification(proof);
    },
    getNotification(proof) {
      return this.$page.props.notifications.find(
        notification => notification.data.proof_id == proof.id,
      );
    },
    getNotificationType(proof) {
      return this.getNotification(proof)?.data.type;
    },
    showProof({ id }) {
      const path = this.route('requester.proofs.show', { id });

      this.$inertia.get(path);
    },

    // 2025-05-20 廃止
    // formatId(id){
    //   return "XP-" + String(id).padStart(5, '0');
    // },

    formatDateTime(dateTime){
      return this.$moment(dateTime).format('YYYY年 M月 D日 H:mm');
    },

    titleFormatter(title) {
    // 件名が37文字以上の場合、37文字で毎回改行する
      if(title.length > 36) {
        return title.match(/.{1,36}/g).join('<br>');
      } else {
        // 37文字以下の場合はそのまま返す
        return title;
      }
    },

    closePdfDownload(proofFile, proof) {
      console.log('closePdfDownload');
      console.log(proofFile);
      const file = proofFile;
      console.log(file[0]);
      const key = file[0].path.startsWith('/')
      ? file[0].path.substring(1)
      : file[0].path;
      const s3 = new S3();
      this.isDownloading = { ...this.isDownloading, [this.getThumbnailId(file)]: true };

      return Promise.resolve(key)
        .then(key => s3.get(key))
        .then(obj => {
          const url = URL.createObjectURL(
            new Blob([obj.Body], { type: 'application/pdf' }),
          );

          const a = document.createElement('a');
          a.href = url;
          a.download = proof.proof_number + '.pdf';
          a.click();
        }).then(() => {
          this.$page.props.snackbar = {
            show: true,
            icon: 'mdi-file-download',
            message: 'ダウンロードを開始しました。',
            color: 'primary',
          };
        })
        .catch(() => {
          this.$page.props.snackbar = {
            show: true,
            icon: 'mdi-alert-circle',
            message: 'ダウンロードに失敗しました。',
            color: 'error',
          };
        })
        .finally(() => {
          this.isDownloading = { ...this.isDownloading, [this.getThumbnailId(file)]: false };
        });
    },

    getThumbnailId(file) {
      console.log(file[0]);
      if(file.length == 0){
        return null;
      }else{
        return file[0].id;
      }
    },

    closedOriginFileZipDownload(item) {
      console.log('closedOriginFileZipDownload');
      console.log(item);

      // Inertiaを使わず、直接ウィンドウを開いてダウンロードする
      console.log(this.user);
      let url = '';
      if (this.user.type_value == 'viewer') {
        url = this.route('requester.proofs.origin-download', { proof: item });
      } else {
        url = this.route(this.user.type.value + '.proofs.origin-download', { proof: item });
      }
      window.open(url, '_blank');
    },

    /**
       * シークレットキーをインポートする
       *
       * @param {string} rawKey
       * @returns {Promise<CryptoKey>}
    */
       async importSecretKey(rawKey) {
        const format = "raw";
        const keyData = new TextEncoder().encode(rawKey);
        const algorithm = {
          name: "HMAC",
          hash: {
            name: "SHA-256",
          },
        };
        const extractable = false;
        const keyUsages = ["sign"];

        return window.crypto.subtle.importKey(
          format,
          keyData,
          algorithm,
          extractable,
          keyUsages
        );
      },

      /**
       * メッセージを署名する
       *
       * @param {CryptoKey} key
       * @param {string} message
       * @returns {Promise<string>}
       */
      async signMessage(key, message) {
        const algorithm = "HMAC";
        const data = new TextEncoder().encode(message);

        const signature = await window.crypto.subtle.sign(algorithm, key, data);

        const buffer = Array.from(new Uint8Array(signature));
        const hashHex = buffer
          .map((byte) => byte.toString(16).padStart(2, "0"))
          .join("");

        return hashHex;
      },

      async requestForm() {
        console.log("requestForm");

        const name = this.$page.props.user.name;
        const email = this.$page.props.user.email;
        const department = this.$page.props.user.department;

        // emailに@が含まれているかチェック
        const is_email = email.includes("@");
            if (!is_email) {
              this.snackbar = {
                message: "メールアドレスが正しくありません",
                color: "error",
              };
              return;
            }

            const data = "EGFUKLVWUJCY" + email; // 固定文字列12文字 + email
            const secret = "A7m5Z_x)";

            const key = await this.importSecretKey(secret);
            const token = await this.signMessage(key, data);

            const query = new URLSearchParams({
              name,
              email,
              token,
              department,
            });

          const url = `/requestform?${query}`;
          // https://domain までのURLを取得
          const host = location.protocol + "//" + location.host + url;
          this.$inertia.get(host);

      },

      back() {
        const url = this.$page.props.urlPrev;

        new URL(url).pathname == '/app/app-key'
          ? history.back()
          : this.$inertia.visit(url);
      },

      isRequester() {
        console.log('🔍 Checking if user is requester in history page');
        console.log('👤 User data:', this.user);
        console.log('🆔 User type ID:', this.user.user_type_id);
        const isReq = this.user.user_type_id == 1;
        console.log('✅ Is requester:', isReq);
        return isReq;
      },
  },

  created() {
    // site.cssの適応を削除する為の処理
    const path = this.route('requestform.index');
    if (this.$page.props.urlPrev == path) {
      window.location.reload();
    }

    this.proofs = this.paginator.data;
    // proofsのinternal_deadline_atをYYYY-MM-DD HH:mm形式に変換
    this.proofs.forEach(proof => {
      proof.internal_deadline_at = this.$moment(proof.internal_deadline_at).format('YYYY-M-D H:mm');
    });
    console.log(this.proofs);
  },

  mounted() {
  }
};
</script>

<style scoped lang="css">
/* .row-pointer >>> tbody > tr:hover {
  cursor: pointer;
} */

.row-pointer ::v-deep(td),
.row-pointer ::v-deep(th) {
  white-space: nowrap;
  padding-left: 10px !important;
  padding-right: 0px !important;
  color: rgba(64,64,64,1) !important

}

.row-pointer ::v-deep(th)
{
  padding-top: 7px !important;
}

.row-pointer ::v-deep(span) {
  font-size: 14px;
}

.row-pointer ::v-deep(tr) {
  height: 60px;
}

.custom-chip {
  border-radius: 5px; /* 角の丸みを調整 */
  margin-bottom: 4px;
  margin-top: 3px;
  padding-top: 2px;

}

.checklist-title ::v-deep(.v-toolbar__content) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.table-text{
  font-size: 14px;
}

.filter-no{
  color: rgba(64, 64, 64, 1);
  font-size: 16px;
  max-width: 10%;
  min-width: -webkit-fill-available;
  border: none;
  border-image: none;
}

.filter-no ::v-deep(.v-input__slot:before),
.filter-no ::v-deep(.v-input__slot:after)  {
  border-image: none !important;  /* 余計なボーダーを消す */
  border-style: none !important;
}

.table-filter::v-deep(.v-label) {
  color: rgba(64,64,64,1) !important;
}

.chip-container{
  display: flex;
  flex-wrap: wrap;
}

.table-text-title{
  font-size: 14px;
  color: rgba(64, 64, 64, 1);
  white-space: break-spaces;
}

.filter-span {
  font-size: 15px;
  font-weight: bold;
  color: rgba(64, 64, 64, 1);
}
</style>