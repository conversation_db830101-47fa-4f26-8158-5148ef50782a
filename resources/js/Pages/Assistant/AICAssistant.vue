<template>
  <div class="app-container">
    <!-- Draggable Character with Preloading Indicator -->
    <div v-if="showDraggableCharacter"
         class="draggable-character"
         :style="{ left: characterPosition.x + 'px', top: characterPosition.y + 'px' }"
         @mousedown="startDrag"
         @touchstart="startDrag"
         @click="handleCharacterClick">

      <!-- Character Canvas (hidden during preloading) -->
      <canvas v-if="!isPreloading"
              ref="characterCanvas"
              class="character-icon"
              :width="characterCanvasSize.width"
              :height="characterCanvasSize.height">
      </canvas>

      <!-- Character Preloading Indicator -->
      <div v-if="isPreloading" class="character-preloading">
        <div class="character-preloading-content">
          <div class="character-preloading-spinner"></div>
          <div v-if="preloadingError" class="character-preloading-error">
            描画エラー
          </div>
        </div>
      </div>
    </div>

    <!-- Speech Bubble -->
    <SpeechBubble
      v-if="showSpeechBubbleComponent"
      ref="speechBubble"
      :position="speechBubblePosition"
      :current-page="getCurrentPageName()"
      @close="closeSpeechBubble"
      @search-performed="handleSearchPerformed"
      @search-completed="handleSearchCompleted"
      @typing-started="handleTypingStarted"
      @typing-stopped="handleTypingStopped"
      @active-typing-started="handleActiveTypingStarted"
      @active-typing-stopped="handleActiveTypingStopped"
      @dimensions-changed="handleDimensionsChanged"
    />

  </div>
</template>

<script>
import SpeechBubble from './Components/SpeechBubble.vue';

export default {
  name: 'AICAssistant',
  components: {
    SpeechBubble
  },
  data() {
    return {
      // Draggable character properties
      showDraggableCharacter: true,
      characterPosition: { x: 0, y: 0 },
      isDragging: false,
      dragOffset: { x: 0, y: 0 },
      dragStartTime: 0,
      dragStartPosition: { x: 0, y: 0 },
      positionUpdateFrame: null,

      // Speech bubble properties
      showSpeechBubbleComponent: false,
      speechBubblePosition: { x: 0, y: 0 },
      speechBubbleDimensions: { width: 335.2, height: 205.6 },

      // Character animation system
      currentBehavior: 'idle',
      currentFrame: 0,
      animationTimer: null,

      // Sprite sheet system
      spriteSheet: null,
      spriteSheetLoaded: false,
      characterCanvasSize: { width: 252, height: 190 },
      frameSize: { width: 252, height: 190 }, // Frame size from new sprite sheet
      spriteSheetCols: 30, // Number of columns in sprite sheet
      spriteSheetRows: 30, // Number of rows in sprite sheet

      // Sprite sheet preloading system
      isPreloading: false,
      preloadingProgress: 0,
      preloadingError: null,

      // Character behaviors with sprite sheet coordinates
      characterBehaviors: {
        // Main idle animation (using idle01)
        idle: {
          startRow: 0, startCol: 0, frameCount: 21,
          duration: 3000,
          loop: true
        },
        // All idle variations
        idle01: {
          startRow: 0, startCol: 0, frameCount: 21,
          duration: 3000,
          loop: true
        },
        idle02: {
          startRow: 0, startCol: 21, frameCount: 40,
          duration: 3000,
          loop: true
        },
        idle03: {
          startRow: 2, startCol: 1, frameCount: 28,
          duration: 3000,
          loop: true
        },
        idle04: {
          startRow: 2, startCol: 29, frameCount: 27,
          duration: 2500,
          loop: true
        },
        idle05: {
          startRow: 3, startCol: 26, frameCount: 32,
          duration: 2500,
          loop: true
        },
        idle06: {
          startRow: 4, startCol: 28, frameCount: 30,
          duration: 2500,
          loop: true
        },
        idle07: {
          startRow: 5, startCol: 28, frameCount: 17,
          duration: 2500,
          loop: true
        },
        idle08: {
          startRow: 6, startCol: 15, frameCount: 24,
          duration: 2500,
          loop: true
        },
        idle09: {
          startRow: 7, startCol: 9, frameCount: 20,
          duration: 2500,
          loop: true
        },
        idle10: {
          startRow: 7, startCol: 29, frameCount: 20,
          duration: 2500,
          loop: true
        },
        idle11: {
          startRow: 8, startCol: 19, frameCount: 20,
          duration: 2500,
          loop: true
        },
        idle12: {
          startRow: 9, startCol: 9, frameCount: 56,
          duration: 2500,
          loop: true
        },
        idle13: {
          startRow: 11, startCol: 5, frameCount: 37,
          duration: 2500,
          loop: true
        },
        // Action animations
        thinking: {
          startRow: 22, startCol: 7, frameCount: 22,
          duration: 2500,
          loop: true
        },
        getAttention: {
          startRow: 12, startCol: 12, frameCount: 29,
          duration: 2000,
          loop: false
        },
        writing: {
          startRow: 22, startCol: 29, frameCount: 22,
          duration: 2000,
          loop: true
        },
        writingSlow: {
          startRow: 22, startCol: 29, frameCount: 22,
          duration: 5328, // Slower version of writing
          loop: true
        },
        searching: {
          startRow: 20, startCol: 13, frameCount: 54,
          duration: 2500,
          loop: true
        },
        reading: {
          startRow: 17, startCol: 18, frameCount: 85,
          duration: 4000,
          loop: false
        },
        look: {
          startRow: 23, startCol: 21, frameCount: 171,
          duration: 6000,
          loop: false
        },
        // Goodbye animations
        goodbye: {
          startRow: 13, startCol: 11, frameCount: 19,
          duration: 2500,
          loop: false
        },
        goodbye01: {
          startRow: 13, startCol: 11, frameCount: 19,
          duration: 2500,
          loop: false
        },
        goodbye02: {
          startRow: 14, startCol: 0, frameCount: 17,
          duration: 2500,
          loop: false
        },
        goodbye03: {
          startRow: 14, startCol: 17, frameCount: 11,
          duration: 2500,
          loop: false
        },
        goodbye04: {
          startRow: 14, startCol: 28, frameCount: 80,
          duration: 4000,
          loop: false
        },
        // Legacy congratulate animations (keeping for compatibility)
        congratulate: {
          startRow: 13, startCol: 11, frameCount: 19,
          duration: 2500,
          loop: false
        },
        congratulate2: {
          startRow: 14, startCol: 0, frameCount: 17,
          duration: 2000,
          loop: false
        }
      },

      isTriggeredBehavior: false,
      isTypingMode: false,
      isActivelyTyping: false,
      isSearchingMode: false,
      behaviorTimeout: null,

      // Idle animation system for positioning
      idleAnimationTimer: null,
      lastUserInteraction: Date.now(),
      currentSide: 'right', // Track which side character is on
    };
  },

  async mounted() {
    console.log('🎬 AICAssistant component mounted!');
    console.log('👁️ showDraggableCharacter:', this.showDraggableCharacter);

    // Initialize character position BEFORE preloading so spinner appears in correct location
    this.initializeCharacterPosition();
    console.log('📍 Initial character position:', this.characterPosition);

    // Initialize current side based on initial position
    this.updateCurrentSide();

    // Start image preloading
    await this.preloadAllImages();

    // Initialize character animation
    this.initializeCharacterAnimation();

    // Initialize idle animation system
    this.initializeIdleAnimationSystem();

    // Add global event listeners for dragging
    document.addEventListener('mousemove', this.handleDrag);
    document.addEventListener('mouseup', this.stopDrag);
    document.addEventListener('touchmove', this.handleDrag);
    document.addEventListener('touchend', this.stopDrag);
  },

  beforeDestroy() {
    // Clean up animation timers
    if (this.animationTimer) {
      clearInterval(this.animationTimer);
    }

    // Clean up behavior timeout
    if (this.behaviorTimeout) {
      clearTimeout(this.behaviorTimeout);
    }

    // Clean up position update frame
    if (this.positionUpdateFrame) {
      cancelAnimationFrame(this.positionUpdateFrame);
    }

    // Clean up idle animation system
    this.cleanupIdleAnimationSystem();

    // Clean up event listeners
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.stopDrag);
    document.removeEventListener('touchmove', this.handleDrag);
    document.removeEventListener('touchend', this.stopDrag);
  },
  methods: {

    // Sprite Sheet Loading Methods
    async preloadAllImages() {
      console.log('🎬 Starting sprite sheet loading...');
      this.isPreloading = true;
      this.preloadingProgress = 0;
      this.preloadingError = null;

      try {
        await this.loadSpriteSheet();
        console.log('✅ Sprite sheet loaded successfully!');
        console.log('🎭 Character should now be visible');
        console.log('📍 Character position:', this.characterPosition);
        console.log('👁️ showDraggableCharacter:', this.showDraggableCharacter);
        console.log('⏳ isPreloading will be set to false');
        this.isPreloading = false;
        this.preloadingProgress = 100;

      } catch (error) {
        console.error('❌ Error loading sprite sheet:', error);
        this.preloadingError = error.message;
        this.isPreloading = false;
      }
    },

    async loadSpriteSheet() {
      return new Promise((resolve, reject) => {
        const img = new Image();

        img.onload = () => {
          this.spriteSheet = img;
          this.spriteSheetLoaded = true;

          // Calculate frame size based on sprite sheet dimensions
          this.frameSize.width = img.width / this.spriteSheetCols;
          this.frameSize.height = img.height / this.spriteSheetRows;

          // Set canvas size to match frame size
          this.characterCanvasSize.width = this.frameSize.width;
          this.characterCanvasSize.height = this.frameSize.height;

          console.log(`📐 Sprite sheet loaded: ${img.width}x${img.height}, Frame size: ${this.frameSize.width}x${this.frameSize.height}`);
          resolve();
        };

        img.onerror = (error) => {
          console.error('❌ Sprite sheet loading failed:', error);
          console.error('❌ Image src was:', img.src);
          reject(new Error('Failed to load sprite sheet'));
        };

        console.log('🔄 Attempting to load sprite sheet from:', '/images/kairu/kairu_sprite_sheet.png');
        img.src = '/images/kairu/kairu_sprite_sheet.png';
      });
    },

    // Legacy image loading methods removed - now using sprite sheet

    // Character Animation Methods
    initializeCharacterAnimation() {
      this.isTriggeredBehavior = false;
      this.setBehavior('idle');
    },

    setBehavior(behaviorName) {
      console.log(`🎬 setBehavior called with: ${behaviorName}`);
      if (!this.characterBehaviors[behaviorName]) {
        console.warn(`Unknown behavior: ${behaviorName}`);
        return;
      }

      this.currentBehavior = behaviorName;
      this.currentFrame = 0;

      const behavior = this.characterBehaviors[behaviorName];
      console.log(`📁 Using behavior: ${behaviorName}, frameCount: ${behavior.frameCount}`);

      this.startFrameAnimation();
    },

    startFrameAnimation() {
      if (this.animationTimer) {
        clearInterval(this.animationTimer);
      }

      const behavior = this.characterBehaviors[this.currentBehavior];
      if (!behavior) return;

      const frameDuration = behavior.duration / behavior.frameCount;

      this.animationTimer = setInterval(() => {
        this.nextFrame();
      }, frameDuration);

      this.updateCharacterImage();
    },

    nextFrame() {
      const behavior = this.characterBehaviors[this.currentBehavior];
      if (!behavior) return;

      this.currentFrame++;

      if (this.currentFrame >= behavior.frameCount) {
        if (behavior.loop || this.isTriggeredBehavior) {
          // For triggered behaviors (like writing), always loop regardless of behavior.loop setting
          this.currentFrame = 0;
        } else {
          this.currentFrame = behavior.frameCount - 1;
          if (!this.isTriggeredBehavior) {
            this.setBehavior('idle');
          }
          return;
        }
      }

      this.updateCharacterImage();
    },

    updateCharacterImage() {
      if (!this.spriteSheetLoaded || !this.spriteSheet) {
        console.warn('⚠️ Sprite sheet not loaded yet');
        return;
      }

      const behavior = this.characterBehaviors[this.currentBehavior];
      if (!behavior) return;

      this.renderSpriteFrame(behavior.startRow, behavior.startCol + this.currentFrame);
    },

    renderSpriteFrame(row, col) {
      console.log(`🎨 renderSpriteFrame called: row=${row}, col=${col}`);
      const canvas = this.$refs.characterCanvas;
      if (!canvas || !this.spriteSheet) {
        console.log(`❌ Canvas or sprite sheet not available: canvas=${!!canvas}, spriteSheet=${!!this.spriteSheet}`);
        return;
      }

      const ctx = canvas.getContext('2d');

      // Clear the canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Calculate source coordinates on sprite sheet
      const sourceX = col * this.frameSize.width;
      const sourceY = row * this.frameSize.height;

      // Draw the frame from sprite sheet to canvas
      ctx.drawImage(
        this.spriteSheet,
        sourceX, sourceY, this.frameSize.width, this.frameSize.height,
        0, 0, canvas.width, canvas.height
      );
    },



    triggerBehavior(behaviorName) {
      console.log(`🎭 triggerBehavior called with: ${behaviorName}, isTypingMode: ${this.isTypingMode}, isSearchingMode: ${this.isSearchingMode}`);

      // Don't interrupt typing mode with other behaviors (except writing variants)
      if (this.isTypingMode && behaviorName !== 'writing' && behaviorName !== 'writingSlow') {
        console.log('❌ Behavior blocked by typing mode');
        return;
      }
      this.isTriggeredBehavior = true;
      this.setBehavior(behaviorName);
      console.log(`✅ Behavior set to: ${behaviorName}`);

      const behavior = this.characterBehaviors[behaviorName];
      if (behavior) {
        // Clear any existing timeout
        if (this.behaviorTimeout) {
          clearTimeout(this.behaviorTimeout);
        }

        const duration = behavior.loop ? 3000 : behavior.duration;
        this.behaviorTimeout = setTimeout(() => {
          // Only return to idle if we're not in typing mode or searching mode
          if (!this.isTypingMode && !this.isSearchingMode) {
            console.log('⏰ Behavior timeout - returning to idle');
            this.isTriggeredBehavior = false;
            this.setBehavior('idle');
          } else {
            console.log('⏰ Behavior timeout - staying in current mode (typing or searching)');
          }
        }, duration);
      }
    },
    getCurrentPageName() {
      try {
        const currentRoute = this.route().current();
        const pageNameMap = {
          'aic.index': 'ホーム',
          'qg.index': '倫理確認システム',
          'requestform.index': '申請フォーム',
          'requestform.history': '申請履歴',
          'member.dashboard.index': 'ダッシュボード',
          'member.proofs.index': 'チェックリスト',
          'member.proofs.archive': 'アーカイブ',
          'admin.dashboard.index': '管理ダッシュボード',
          'cc.index': 'カードチェック',
        };
        return pageNameMap[currentRoute] || 'ホーム';
      } catch (error) {
        return 'ホーム';
      }
    },

    // Get viewport dimensions that account for layout differences
    getViewportDimensions() {
      // Use document.documentElement for more reliable viewport calculation
      // This accounts for different layouts (with/without headers/sidebars)
      return {
        width: document.documentElement.clientWidth || window.innerWidth,
        height: document.documentElement.clientHeight || window.innerHeight
      };
    },

    // Draggable character methods
    initializeCharacterPosition() {
      const characterSize = 150; // Default character size
      const bubbleHeight = 205.6; // Speech bubble height
      const tailHeight = 35; // Tail height
      const safeGap = 2; // Gap between tail tip and character
      const safeMargin = 60; // Safe margin from edges

      // Get consistent viewport dimensions across different layouts
      const viewport = this.getViewportDimensions();
      console.log('🖥️ Viewport dimensions:', viewport);

      // Calculate position that leaves room for speech bubble above
      const rightX = viewport.width - characterSize - safeMargin - 56; // Moved 56px more to the left
      const bottomY = viewport.height - characterSize - safeMargin;

      console.log('📐 Position calculation:');
      console.log('  - characterSize:', characterSize);
      console.log('  - safeMargin:', safeMargin);
      console.log('  - rightX calculation:', `${viewport.width} - ${characterSize} - ${safeMargin} - 56 = ${rightX}`);
      console.log('  - bottomY calculation:', `${viewport.height} - ${characterSize} - ${safeMargin} = ${bottomY}`);

      // Ensure there's enough space above for the speech bubble
      // Total space needed: bubble + tail + gap + character + margin
      const totalSpaceNeeded = bubbleHeight + tailHeight + safeGap + characterSize + safeMargin;
      const minYForBubble = totalSpaceNeeded - characterSize; 
      const adjustedY = Math.max(minYForBubble, bottomY);

      // Temporary fix: Use safe, visible position for testing
      const safeX = Math.min(rightX, viewport.width - 200); // Ensure at least 200px from right edge
      const safeY = Math.min(adjustedY, viewport.height - 200); // Ensure at least 200px from bottom

      this.characterPosition = {
        x: Math.max(50, safeX), // Ensure at least 50px from left edge
        y: Math.max(50, safeY)  // Ensure at least 50px from top
      };

      console.log('✅ Final character position:', this.characterPosition);
    },

    startDrag(event) {
      // Don't allow dragging during preloading
      if (this.isPreloading) {
        return;
      }

      this.isDragging = true;
      this.dragStartTime = Date.now();

      // Pause idle animation tracking while dragging
      if (this.idleAnimationTimer) {
        clearTimeout(this.idleAnimationTimer);
      }

      // Get the mouse/touch position
      const clientX = event.type === 'touchstart' ? event.touches[0].clientX : event.clientX;
      const clientY = event.type === 'touchstart' ? event.touches[0].clientY : event.clientY;

      // Store initial drag position
      this.dragStartPosition = { x: clientX, y: clientY };

      // Calculate offset from character position to mouse/touch position
      this.dragOffset = {
        x: clientX - this.characterPosition.x,
        y: clientY - this.characterPosition.y
      };

      // Prevent default to avoid text selection
      event.preventDefault();
    },

    handleDrag(event) {
      if (!this.isDragging) return;

      // Get the mouse/touch position
      const clientX = event.type === 'touchmove' ? event.touches[0].clientX : event.clientX;
      const clientY = event.type === 'touchmove' ? event.touches[0].clientY : event.clientY;

      // Calculate new position
      let newX = clientX - this.dragOffset.x;
      let newY = clientY - this.dragOffset.y;

      // Keep character within viewport bounds
      const viewport = this.getViewportDimensions();
      const characterSize = 80;
      newX = Math.max(0, Math.min(viewport.width - characterSize, newX));
      newY = Math.max(0, Math.min(viewport.height - characterSize, newY));

      this.characterPosition = {
        x: newX,
        y: newY
      };
      if (this.positionUpdateFrame) {
        cancelAnimationFrame(this.positionUpdateFrame);
      }

      this.positionUpdateFrame = requestAnimationFrame(() => {
        if (this.showSpeechBubbleComponent) {
          this.calculateSpeechBubblePosition();
        }
      });

      event.preventDefault();
    },

    stopDrag() {
      this.isDragging = false;

      // Check for side change and trigger appropriate animations
      this.handleSideChange();

      // Restart idle animation tracking after dragging stops
      this.startIdleAnimationTracking();
    },

    handleSideChange() {
      const isOnRightSide = this.isCharacterOnRightSide();
      const isOnLeftSide = this.isCharacterOnLeftSide();
      const previousSide = this.currentSide;

      // Update current side
      if (isOnRightSide) {
        this.currentSide = 'right';
      } else if (isOnLeftSide) {
        this.currentSide = 'left';
      } else {
        this.currentSide = 'center';
      }

      // Trigger animations based on side changes
      if (this.currentSide !== previousSide) {
        console.log(`🔄 Side changed from ${previousSide} to ${this.currentSide}`);

        if (this.currentSide === 'left' && previousSide !== 'left') {
          // Character moved to left side - trigger idle_02 immediately
          console.log(`🎭 Character moved to left side - triggering idle_02`);
          this.triggerBehavior('idle02');
        } else if (this.currentSide === 'right' && previousSide !== 'right') {
          // Character moved to right side - return to normal idle_01
          console.log(`🎭 Character moved to right side - returning to idle_01`);
          this.isTriggeredBehavior = false;
          this.setBehavior('idle');
        } else if (this.currentSide === 'center') {
          // Character in center - return to normal idle_01
          console.log(`🎭 Character in center - returning to idle_01`);
          this.isTriggeredBehavior = false;
          this.setBehavior('idle');
        }
      }
    },

    handleCharacterClick(event) {
      // Don't allow clicking during preloading
      if (this.isPreloading) {
        return;
      }

      const dragDuration = Date.now() - this.dragStartTime;
      const dragDistance = Math.sqrt(
        Math.pow(event.clientX - this.dragStartPosition.x, 2) +
        Math.pow(event.clientY - this.dragStartPosition.y, 2)
      );

      // More lenient detection: Only consider it a drag if significant movement occurred
      // AND it took more than a brief moment (to allow for normal clicks)
      if (dragDuration > 500 && dragDistance > 20) {
        // This was a clear drag operation, don't trigger click actions
        return;
      }

      // This was a genuine click or very short drag, proceed with normal click behavior
      this.toggleSpeechBubble();
    },

    toggleSpeechBubble() {
      if (this.showSpeechBubbleComponent) {
        this.closeSpeechBubble();
      } else {
        this.showSpeechBubble();
        this.triggerBehavior('getAttention');
      }
    },

    showSpeechBubble() {
      this.calculateSpeechBubblePosition();

      this.showSpeechBubbleComponent = true;

      // Pause idle animation tracking while speech bubble is open
      if (this.idleAnimationTimer) {
        clearTimeout(this.idleAnimationTimer);
      }
    },

    // Calculate speech bubble position relative to draggable character
    calculateSpeechBubblePosition() {
      const bubbleWidth = this.speechBubbleDimensions.width;
      const bubbleHeight = this.speechBubbleDimensions.height;
      const characterSize = 150; // Character container size
      const tailHeight = 20; 
      const safeGap = 2; // 2px gap between tail tip and character
      const centerMargin = 0; // No margin restrictions - allow full screen usage

      // Position bubble to ensure it's fully visible, then position character below it
      const characterCenterX = this.characterPosition.x + (characterSize / 2);
      const bubbleX = characterCenterX - (bubbleWidth / 2);

      // Calculate bubble Y position to ensure it's fully visible
      const viewport = this.getViewportDimensions();
      const minBubbleY = centerMargin;
      const maxBubbleY = viewport.height - bubbleHeight - tailHeight - characterSize - safeGap - centerMargin;

      // Position bubble optimally within screen bounds
      const optimalBubbleY = this.characterPosition.y - bubbleHeight - tailHeight - safeGap;
      const bubbleY = Math.max(minBubbleY, Math.min(maxBubbleY, optimalBubbleY));

      // Clamp X position to ensure bubble is fully visible, accounting for character's left shift
      const minX = centerMargin;
      const maxX = viewport.width - bubbleWidth - centerMargin;
      // Apply the same 56px left shift to the bubble positioning
      const adjustedX = Math.max(minX, Math.min(maxX, bubbleX - 56));

      this.speechBubblePosition = {
        x: adjustedX,
        y: bubbleY
      };
      const tailTipY = bubbleY + bubbleHeight + tailHeight;
      this.characterPosition.y = tailTipY + safeGap;
    },

    closeSpeechBubble() {
      this.showSpeechBubbleComponent = false;
      this.speechBubbleDimensions = { width: 335.2, height: 205.6 };

      // Restart idle animation tracking when speech bubble is closed
      this.startIdleAnimationTracking();
    },

    handleSearchPerformed() {
      console.log('🔍 Search performed - starting thinking behavior');
      this.calculateSpeechBubblePosition();

      // Exit typing mode first to allow thinking behavior to be triggered
      this.isTypingMode = false;
      this.isActivelyTyping = false;
      console.log('📝 Typing mode disabled');

      // Set searching mode to keep thinking behavior active
      this.isSearchingMode = true;
      console.log('🔄 Search mode activated');

      // Switch to thinking behavior immediately when search is performed
      this.triggerBehavior('thinking');
      console.log('🤔 Thinking behavior triggered');
    },

    handleSearchCompleted(result = {}) {
      // Search is complete, exit searching mode
      this.isSearchingMode = false;
      this.isTriggeredBehavior = false;

      // If ChatGPT gave a successful response, trigger congratulate animation
      if (result.success && result.hasResponse) {
        console.log('🎉 ChatGPT successful response - triggering congratulate animation');
        // Randomly choose between congratulate and congratulate2
        const congratulateAnimations = ['congratulate', 'congratulate2'];
        const randomAnimation = congratulateAnimations[Math.floor(Math.random() * congratulateAnimations.length)];
        this.triggerBehavior(randomAnimation);
      } else {
        // Return to idle behavior for unsuccessful responses
        this.setBehavior('idle');
      }
    },

    handleTypingStarted() {
      // Start writing behavior and let it loop continuously
      if (this.behaviorTimeout) {
        clearTimeout(this.behaviorTimeout);
        this.behaviorTimeout = null;
      }
      this.isTypingMode = true;
      this.isTriggeredBehavior = true;
      // Start with slow animation by default (when just focused)
      // Will switch to normal speed when user starts actively typing
      this.setBehavior('idle');
    },

    handleTypingStopped() {
      // Stop writing behavior and return to idle
      this.isTypingMode = false;
      this.isActivelyTyping = false;
      this.isTriggeredBehavior = false;
      this.setBehavior('idle');
    },

    handleActiveTypingStarted() {
      // User started actively typing - switch to normal speed writing animation
      this.isActivelyTyping = true;
      if (this.isTypingMode) {
        this.setBehavior('writing'); // Normal speed
      }
    },

    handleActiveTypingStopped() {
      // User stopped actively typing but textarea is still focused - switch to slow animation
      this.isActivelyTyping = false;
      if (this.isTypingMode) {
        this.setBehavior('idle'); // Slow speed (3 FPS) writingSlow
      }
    },

    handleDimensionsChanged(dimensions) {
      // Update speech bubble dimensions with real-time values
      this.speechBubbleDimensions = {
        width: dimensions.width,
        height: dimensions.height
      };

      // Recalculate position to maintain 2px gap
      if (this.showSpeechBubbleComponent) {
        this.calculateSpeechBubblePosition();
      }
    },

    // Idle Animation System Methods
    initializeIdleAnimationSystem() {
      // Start tracking user interactions for idle animations
      this.startIdleAnimationTracking();
      this.setupUserInteractionListeners();
    },

    startIdleAnimationTracking() {
      // Clear any existing timer
      if (this.idleAnimationTimer) {
        clearTimeout(this.idleAnimationTimer);
      }

      // Don't start timer if character is on left side and already using idle_03
      if (this.isCharacterOnLeftSide() &&
          this.currentBehavior === 'idle03') {
        console.log(` Skipping idle timer - character on left side using idle_03`);
        return;
      }

      // Set up 30-second timer for idle animations
      this.idleAnimationTimer = setTimeout(() => {
        this.checkForIdleAnimation();
      }, 30000); // 30 seconds
    },

    checkForIdleAnimation() {
      // Check if character is in a valid idle state
      if (!this.isTypingMode &&
          !this.isSearchingMode &&
          !this.showSpeechBubbleComponent) {

        const isOnRightSide = this.isCharacterOnRightSide();
        const isOnLeftSide = this.isCharacterOnLeftSide();

        // Right side: trigger idle_09 or idle_10 after 30 seconds in idle
        if (isOnRightSide &&
            this.currentBehavior === 'idle') {

          const idleAnimations = ['idle09', 'idle10'];
          const randomAnimation = idleAnimations[Math.floor(Math.random() * idleAnimations.length)];

          console.log(`🎭 Right side: Triggering idle animation: ${randomAnimation}`);
          this.triggerIdleAnimation(randomAnimation);
          return;
        }

        // Left side: use idle_03 for continuous idle after 30 seconds
        if (isOnLeftSide &&
            this.currentBehavior === 'idle') {

          console.log(`🎭 Left side: Switching to idle_03 for continuous idle`);
          this.triggerLeftSideIdleAnimation();
          return;
        }
      }

      // Restart the timer if conditions aren't met
      this.startIdleAnimationTracking();
    },

    triggerIdleAnimation(animationName) {
      // Set the triggered behavior flag to prevent interruption
      this.isTriggeredBehavior = true;
      this.setBehavior(animationName);

      const behavior = this.characterBehaviors[animationName];
      if (behavior) {
        // After animation completes, return to appropriate idle state
        setTimeout(() => {
          console.log(`✅ Idle animation ${animationName} completed`);
          this.isTriggeredBehavior = false;

          // Return to appropriate idle state based on current position
          if (animationName === 'idle02') {
            // After idle_02 completes on left side, switch to idle_03 for continuous animation
            console.log(`🔄 idle_02 completed, switching to idle_03 for continuous left-side animation`);
            this.setBehavior('idle03');
          } else {
            // For right-side animations (idle09, idle10), return to idle_01
            console.log(`🔄 Right-side animation completed, returning to idle_01`);
            this.setBehavior('idle');
            // Restart the idle animation tracking for right side
            this.startIdleAnimationTracking();
          }
        }, behavior.duration);
      }
    },

    isCharacterOnRightSide() {
      // Check if character is positioned on the right side of the screen
      const viewport = this.getViewportDimensions();
      const characterCenterX = this.characterPosition.x + 75; // Half of character size (150px)
      const rightSideThreshold = viewport.width * 0.7; // Consider right side as 70% from left

      return characterCenterX >= rightSideThreshold;
    },

    isCharacterOnLeftSide() {
      // Check if character is positioned on the left side of the screen
      const viewport = this.getViewportDimensions();
      const characterCenterX = this.characterPosition.x + 75; // Half of character size (150px)
      const leftSideThreshold = viewport.width * 0.3; // Consider left side as 30% from left

      return characterCenterX <= leftSideThreshold;
    },

    triggerLeftSideIdleAnimation() {
      // Switch to idle_03 for continuous left-side idle animation
      this.isTriggeredBehavior = false; // Allow normal looping behavior
      this.setBehavior('idle03');
      console.log(`✅ Left side: Switched to idle_03 continuous animation`);

      // Don't restart idle tracking since idle_03 should continue looping
    },

    updateCurrentSide() {
      // Update current side based on character position
      if (this.isCharacterOnRightSide()) {
        this.currentSide = 'right';
      } else if (this.isCharacterOnLeftSide()) {
        this.currentSide = 'left';
      } else {
        this.currentSide = 'center';
      }
      console.log(`📍 Current side updated to: ${this.currentSide}`);
    },

    setupUserInteractionListeners() {
      // Listen for user interactions to reset idle timer
      const interactionEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

      interactionEvents.forEach(event => {
        document.addEventListener(event, this.handleUserInteraction, true);
      });
    },

    handleUserInteraction() {
      // Update last interaction time and restart idle timer
      this.lastUserInteraction = Date.now();
      this.startIdleAnimationTracking();
    },

    cleanupIdleAnimationSystem() {
      // Clean up timers and event listeners
      if (this.idleAnimationTimer) {
        clearTimeout(this.idleAnimationTimer);
      }

      const interactionEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
      interactionEvents.forEach(event => {
        document.removeEventListener(event, this.handleUserInteraction, true);
      });
    }

  },

  beforeUnmount() {
    // Clean up animation timers
    if (this.animationTimer) {
      clearInterval(this.animationTimer);
    }

    // Clean up behavior timeout
    if (this.behaviorTimeout) {
      clearTimeout(this.behaviorTimeout);
    }

    // Clean up position update frame
    if (this.positionUpdateFrame) {
      cancelAnimationFrame(this.positionUpdateFrame);
    }

    // Clean up idle animation system
    this.cleanupIdleAnimationSystem();

    // Clean up event listeners
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.stopDrag);
    document.removeEventListener('touchmove', this.handleDrag);
    document.removeEventListener('touchend', this.stopDrag);

    // Clean up sprite sheet
    if (this.spriteSheet) {
      this.spriteSheet = null;
    }
  }
};
</script>

<style>
.app-container {
  font-family: 'Roboto, sans-serif';/*'Inter', 'SF Pro Display', 'Segoe UI', sans-serif;*/
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

/* Character Preloading Styles */
.character-preloading {
  width: 170px;
  height: 128px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 10px;
  /* background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 2px solid #1ABC9C;
  backdrop-filter: blur(2px); */
}

.character-preloading-content {
  text-align: center;
  padding: 0;
  width: auto;
}

.character-preloading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1ABC9C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.character-preloading-text {
  font-size: 12px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
  font-family: 'Noto Sans JP', sans-serif;
}

.character-preloading-progress {
  margin-bottom: 4px;
}

.character-progress-bar {
  width: 100%;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.character-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1ABC9C, #16A085);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.character-progress-text {
  font-size: 10px;
  color: #666;
  font-weight: 500;
}

.character-preloading-error {
  color: #e74c3c;
  font-size: 10px;
  margin-top: 4px;
  padding: 4px;
  background: #fdf2f2;
  border-radius: 4px;
  border: 1px solid #fecaca;
}

/* Draggable Character Styles */
.draggable-character {
  position: fixed;
  z-index: 9999;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  /* Remove transition to prevent movement animation on page load */
  width: 170px;
  height: 128px; /*keep aspect ratio*/
}

/* Character icon styling for PNG images with transparency */
.character-icon[src*=".png"] {
  /* Add subtle drop shadow for better visibility */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));

  /* Ensure transparent background is maintained */
  background-color: transparent !important;
  background-image: none !important;
}
.draggable-character .character-icon {
  /* HOT FIX, we shouldn't have to resize each if the images are correctly set */
  width: 170px;
  height: 128px;

  pointer-events: none;
  transition: all 0.3s ease;
}
</style>